/*
 * @Author: zhouming <EMAIL>
 * @Date: 2025-08-26 11:25:09
 * @LastEditors: zhouming <EMAIL>
 * @LastEditTime: 2025-08-30 15:32:32
 * @FilePath: \wp-sitemgr\app\api\dns\check\route.ts
 * @Description: 
 * 
 * Copyright (c) 2025 by zhixin tech, All Rights Reserved. 
 */
import { NextResponse } from 'next/server';
import { dnsConfig } from '@/app/lib/config';
import dns from 'dns/promises';
import { lookup, resolve4, resolve6, resolveMx, resolveTxt, resolveCname, resolveNs, resolveSoa } from 'dns/promises';


// POST /api/dns/check - Check DNS resolution
export async function POST(request: Request) {
  try {
    let resolved = false;
    let records: String[] = [];
    let dnsType = 'A';
    const { domain } = await request.json();
    
    if (!domain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }
    
    let result = await resolve4(domain);
    if (result.length > 0) {
      resolved = true;
      records = result;
    }else {
      result = await  resolveCname(domain);
      if (result.length > 0) {
        resolved = true;
        records = result;
        dnsType = 'CNAME';
      }
    }

    return NextResponse.json({
      domain,
      resolved,
      dnsType,
      records,
      message: resolved ? 'Domain is resolved' : 'Domain is not resolved'
    });
  } catch (error) {
    console.error('DNS check error:', error);
    if (error.code === 'ENOTFOUND') {
      return NextResponse.json({
        // domain,
        resolved: false,
        dnsType: 'A',
        records: [],
        message: 'Domain is not resolved'
      });
    }
    return NextResponse.json(
      { error: 'Failed to check DNS resolution' },
      { status: 500 }
    );
  }
}

function getRecordType(type: number): string {
  const types: { [key: number]: string } = {
    1: 'A',
    28: 'AAAA',
    5: 'CNAME',
    15: 'MX',
    16: 'TXT',
    2: 'NS'
  };
  return types[type] || 'UNKNOWN';
}
