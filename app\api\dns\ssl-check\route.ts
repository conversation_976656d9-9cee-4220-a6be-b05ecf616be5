import { NextResponse } from 'next/server';

// POST /api/dns/ssl-check - Check SSL status via Cloudflare API
export async function POST(request: Request) {
  try {
    const { domain } = await request.json();
    
    if (!domain) {
      return NextResponse.json(
        { error: 'Domain is required' },
        { status: 400 }
      );
    }
    
    // Get Cloudflare API credentials from environment
    const cfApiToken = process.env.CLOUDFLARE_API_TOKEN;
    const cfZoneId = process.env.CLOUDFLARE_ZONE_ID;
    
    if (!cfApiToken || !cfZoneId) {
      // Fallback to simulated SSL check
      return simulateSslCheck(domain);
    }
    
    try {
      // Check SSL settings via Cloudflare API
      const sslResponse = await fetch(`https://api.cloudflare.com/client/v4/zones/${cfZoneId}/settings/ssl`, {
        headers: {
          'Authorization': `Bearer ${cfApiToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!sslResponse.ok) {
        throw new Error('Cloudflare API request failed');
      }
      
      const sslData = await sslResponse.json();
      
      // Get certificate info
      const certResponse = await fetch(`https://api.cloudflare.com/client/v4/zones/${cfZoneId}/ssl/certificate_packs`, {
        headers: {
          'Authorization': `Bearer ${cfApiToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      let certInfo = null;
      if (certResponse.ok) {
        const certData = await certResponse.json();
        if (certData.result && certData.result.length > 0) {
          const cert = certData.result[0];
          certInfo = {
            issuer: cert.certificate_authority,
            expiresAt: cert.expires_on
          };
        }
      }
      
      return NextResponse.json({
        domain,
        status: sslData.result?.value === 'off' ? 'none' : 'active',
        type: sslData.result?.value || 'none',
        issuer: certInfo?.issuer || null,
        expiresAt: certInfo?.expiresAt || null
      });
      
    } catch (cfError) {
      console.error('Cloudflare API error:', cfError);
      return simulateSslCheck(domain);
    }
    
  } catch (error) {
    console.error('SSL check error:', error);
    return NextResponse.json(
      { error: 'Failed to check SSL status' },
      { status: 500 }
    );
  }
}

function simulateSslCheck(domain: string) {
  // Simulate SSL check for demo purposes
  const sslTypes = ['flexible', 'full', 'strict', 'none'];
  const statuses = ['active', 'pending', 'error', 'none'];
  
  const randomType = sslTypes[Math.floor(Math.random() * sslTypes.length)];
  const randomStatus = randomType === 'none' ? 'none' : statuses[Math.floor(Math.random() * 3)];
  
  return NextResponse.json({
    domain,
    status: randomStatus,
    type: randomType,
    issuer: randomStatus === 'active' ? 'Let\'s Encrypt' : null,
    expiresAt: randomStatus === 'active' ? new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString() : null
  });
}